import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { supabaseServiceClient } from '$lib/server/supabase_client';
import { error, json } from '@sveltejs/kit';
import crypto from 'crypto';
import { inviteRequestSchema } from '$lib/schemas/invite-request';
import { sendEmail } from '$lib/server/email';
import { PUBLIC_BASE_URL } from '$env/static/public';

export const POST: RequestHandler = async ({ request, locals }) => {
	const userId = locals.user?.id;
	if (!userId) throw error(401, 'Not authenticated');

	const reqJson = await request.json();
	const parsed = inviteRequestSchema.safeParse(reqJson);
	if (!parsed.success) {
		console.error('Invalid invite request:', parsed.error);
		throw error(400, parsed.error.message);
	}
	const { resourceType, resourceId, role, inviteeEmail, expiresAt } = parsed.data;

	let joinWhat = '';
	// Validate inviter rights based on resourceType and resourceId
	switch (resourceType) {
		case 'organization': {
			const { data, error: authError } = await locals.supabase.rpc('current_user_has_entity_role', {
				entity_type_param: 'organization',
				entity_id_param: resourceId,
				min_role_param: 'admin',
			});
			if (authError) {
				console.error('Error validating inviter rights:', authError);
				throw error(403, 'Insufficient permissions');
			}
			if (!data) {
				console.error('Insufficient permissions');
				throw error(403, 'Insufficient permissions');
			}
			joinWhat = 'an organization';
			break;
		}
		case 'client': {
			const { data, error: authError } = await locals.supabase.rpc('get_effective_role', {
				user_id_param: userId,
				entity_type_param: 'client',
				entity_id_param: resourceId,
			});
			if (authError) {
				console.error('Error validating inviter rights:', authError);
				throw error(403, 'Insufficient permissions');
			}
			if (!['admin', 'owner'].includes(data)) {
				console.error('Insufficient permissions');
				throw error(403, 'Insufficient permissions');
			}
			joinWhat = 'a client';
			break;
		}
		case 'project': {
			const { data, error: authError } = await locals.supabase.rpc('is_project_owner', {
				project_id_param: resourceId,
			});
			if (authError) {
				console.error('Error validating inviter rights:', authError);
				throw error(403, 'Insufficient permissions');
			}
			if (!data) {
				console.error('Insufficient permissions');
				throw error(403, 'Insufficient permissions');
			}
			joinWhat = 'a project';
			break;
		}
		default: {
			console.error('Invalid resource type:', resourceType);
			throw error(403, 'Insufficient permissions');
		}
	}

	// Generate and hash token
	const token = crypto.randomUUID();
	const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

	const { error: insertError } = await supabaseServiceClient.from('invite').insert({
		resource_type: resourceType,
		resource_id: resourceId,
		role,
		invitee_email: inviteeEmail,
		token_hash: tokenHash,
		expires_at: expiresAt,
		inviter_id: userId,
		updated_by: userId,
	});

	if (insertError) throw error(500, insertError.message);

	// send email
	const res = await sendEmail({
		to: inviteeEmail,
		subject: `You have been invited to join ${joinWhat} on Cost Atlas`,
		html: `<p>You have been invited to join ${joinWhat}. Click <a href="${PUBLIC_BASE_URL}/auth/invite/${encodeURIComponent(token)}">here</a> to accept or decline the invitation.</p>`,
	});

	if (res.error) {
		console.error('Error sending email:', res.error);
		throw error(500, 'Error sending email');
	}

	return json({
		success: res.success,
	});
};
